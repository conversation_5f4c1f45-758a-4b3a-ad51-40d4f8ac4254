import { useState } from "react";

interface Day {
  name: string;
  date: number;
}

const days: Day[] = [
  { name: "We<PERSON>", date: 15 },
  { name: "<PERSON><PERSON>", date: 16 },
  { name: "<PERSON><PERSON>", date: 17 },
  { name: "<PERSON><PERSON>", date: 18 },
  { name: "<PERSON>", date: 19 },
  { name: "<PERSON>", date: 20 },
  { name: "<PERSON><PERSON>", date: 21 },
];

export const DateSelector = () => {
  const [selectedDate, setSelectedDate] = useState(20);

  return (
    <div className="flex justify-between items-center px-4 py-6">
      {days.map((day) => (
        <button
          key={day.date}
          onClick={() => setSelectedDate(day.date)}
          className={`flex flex-col items-center transition-all duration-300 ${
            selectedDate === day.date
              ? "scale-105"
              : "opacity-50 hover:opacity-75"
          }`}
        >
          <span className="text-xs font-sans text-muted-foreground mb-2">
            {day.name}
          </span>
          <div
            className={`w-12 h-12 rounded-full flex items-center justify-center font-serif text-lg transition-all duration-300 ${
              selectedDate === day.date
                ? "bg-card shadow-soft"
                : "border border-dashed border-muted"
            }`}
          >
            {day.date}
          </div>
        </button>
      ))}
    </div>
  );
};
