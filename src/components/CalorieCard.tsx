import { Flame } from "lucide-react";

export const CalorieCard = () => {
  const caloriesLeft = 1450;
  const totalCalories = 2000;
  const progress = ((totalCalories - caloriesLeft) / totalCalories) * 100;

  return (
    <div className="mx-4 p-8 bg-card rounded-[40px] shadow-card flex items-center justify-between">
      <div>
        <h2 className="text-6xl font-serif font-bold text-foreground mb-2">
          {caloriesLeft}
        </h2>
        <p className="text-sm font-sans text-muted-foreground">Calories left</p>
      </div>

      <div className="relative w-28 h-28">
        <svg className="w-full h-full transform -rotate-90">
          <circle
            cx="56"
            cy="56"
            r="50"
            className="stroke-muted fill-none"
            strokeWidth="8"
          />
          <circle
            cx="56"
            cy="56"
            r="50"
            className="stroke-primary fill-none transition-all duration-500"
            strokeWidth="8"
            strokeDasharray={`${2 * Math.PI * 50}`}
            strokeDashoffset={`${2 * Math.PI * 50 * (1 - progress / 100)}`}
            strokeLinecap="round"
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-20 h-20 bg-background rounded-full flex items-center justify-center">
            <Flame className="w-8 h-8 text-primary" />
          </div>
        </div>
      </div>
    </div>
  );
};
