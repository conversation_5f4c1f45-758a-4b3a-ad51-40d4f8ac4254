import { <PERSON>, <PERSON>, Setting<PERSON>, Plus } from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";

export const BottomNav = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const isActive = (path: string) => location.pathname === path;

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 bg-card border-t border-border z-40">
        <div className="flex items-center justify-around h-20 px-4">
          <button
            onClick={() => navigate("/")}
            className={`flex flex-col items-center gap-1 transition-colors ${
              isActive("/") ? "" : "opacity-50 hover:opacity-100"
            }`}
          >
            <Home
              className={`w-6 h-6 ${
                isActive("/") ? "text-foreground" : "text-muted-foreground"
              }`}
            />
            <span
              className={`text-xs font-sans font-medium ${
                isActive("/") ? "text-foreground" : "text-muted-foreground"
              }`}
            >
              Home
            </span>
          </button>

          <button
            onClick={() => navigate("/history")}
            className={`flex flex-col items-center gap-1 transition-colors ${
              isActive("/history") ? "" : "opacity-50 hover:opacity-100"
            }`}
          >
            <Clock
              className={`w-6 h-6 ${
                isActive("/history")
                  ? "text-foreground"
                  : "text-muted-foreground"
              }`}
            />
            <span
              className={`text-xs font-sans ${
                isActive("/history")
                  ? "text-foreground font-medium"
                  : "text-muted-foreground"
              }`}
            >
              History
            </span>
          </button>

          <button className="flex flex-col items-center gap-1 transition-colors opacity-50 hover:opacity-100">
            <Settings className="w-6 h-6 text-muted-foreground" />
            <span className="text-xs font-sans text-muted-foreground">
              Settings
            </span>
          </button>
        </div>
      </div>

      {/* Floating Action Button */}
      <button className="fixed bottom-24 right-6 w-14 h-14 bg-foreground rounded-full flex items-center justify-center shadow-card hover:scale-105 transition-transform z-50">
        <Plus className="w-7 h-7 text-background" />
      </button>
    </>
  );
};
