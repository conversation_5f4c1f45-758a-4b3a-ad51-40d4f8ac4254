import { Bell, X } from "lucide-react";

export const ScanHistory = () => {
  return (
    <div className="mx-4 mt-8">
      <h2 className="text-2xl font-serif font-semibold text-foreground mb-4">
        Recently scanned
      </h2>

      {/* Notification Card */}
      <div className="bg-card rounded-[32px] shadow-soft p-4 flex items-start gap-3 mb-4">
        <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center flex-shrink-0 mt-1">
          <Bell className="w-5 h-5 text-muted-foreground" />
        </div>
        <div className="flex-1">
          <p className="text-sm font-sans text-foreground leading-relaxed">
            You can switch apps or turn off your phone. We'll notify you when the analysis is done.
          </p>
        </div>
        <button className="text-muted-foreground hover:text-foreground transition-colors flex-shrink-0">
          <X className="w-5 h-5" />
        </button>
      </div>

      {/* Analysis Card */}
      <div className="bg-card rounded-[32px] shadow-soft p-4 flex gap-4">
        <div className="relative w-32 h-32 bg-gradient-to-br from-primary to-secondary rounded-3xl flex items-center justify-center flex-shrink-0">
          <div className="relative w-20 h-20">
            <svg className="w-full h-full transform -rotate-90">
              <circle
                cx="40"
                cy="40"
                r="36"
                className="stroke-white/20 fill-none"
                strokeWidth="6"
              />
              <circle
                cx="40"
                cy="40"
                r="36"
                className="stroke-white fill-none transition-all duration-500"
                strokeWidth="6"
                strokeDasharray={`${2 * Math.PI * 36}`}
                strokeDashoffset={`${2 * Math.PI * 36 * 0.76}`}
                strokeLinecap="round"
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-white font-serif text-xl font-semibold">24%</span>
            </div>
          </div>
        </div>

        <div className="flex-1 flex flex-col justify-center">
          <h3 className="font-sans font-medium text-foreground mb-3">
            Analyzing product...
          </h3>
          <div className="space-y-2">
            <div className="h-2 bg-muted rounded-full w-full"></div>
            <div className="h-2 bg-muted rounded-full w-3/4"></div>
            <div className="h-2 bg-muted rounded-full w-1/2"></div>
          </div>
          <p className="text-xs font-sans text-muted-foreground mt-3">
            We'll notify you when done!
          </p>
        </div>
      </div>
    </div>
  );
};
