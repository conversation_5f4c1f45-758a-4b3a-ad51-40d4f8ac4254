import { Droplets } from "lucide-react";

export const SkinTypeCard = () => {
  return (
    <div className="mx-4 mt-4 p-8 bg-card rounded-[40px] shadow-card relative overflow-hidden">
      {/* Organic blob background */}
      <div className="absolute top-0 right-0 w-40 h-40 bg-primary/10 rounded-full blur-3xl" />
      <div className="absolute bottom-0 left-0 w-32 h-32 bg-secondary/10 rounded-full blur-3xl" />
      
      <div className="relative z-10">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 mb-4">
          <Droplets className="w-8 h-8 text-primary" />
        </div>
        
        <h2 className="text-2xl font-serif font-semibold text-foreground mb-2">
          Your Skin Type
        </h2>
        
        <p className="text-4xl font-serif font-bold text-foreground mb-3">
          Acne-Prone & Sensitive
        </p>
        
        <p className="text-sm font-sans text-muted-foreground">
          Personalized care for your unique skin needs
        </p>
      </div>
    </div>
  );
};
