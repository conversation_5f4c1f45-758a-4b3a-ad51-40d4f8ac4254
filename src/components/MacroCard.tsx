import { LucideIcon } from "lucide-react";

interface MacroCardProps {
  amount: number;
  unit: string;
  label: string;
  icon: LucideIcon;
  color: "primary" | "secondary" | "tertiary";
}

export const MacroCard = ({ amount, unit, label, icon: Icon, color }: MacroCardProps) => {
  const colorClasses = {
    primary: "text-primary bg-primary/10",
    secondary: "text-secondary bg-secondary/10",
    tertiary: "text-tertiary bg-tertiary/10",
  };

  return (
    <div className="bg-card rounded-[32px] shadow-soft p-6 flex flex-col items-start">
      <div className="mb-4">
        <h3 className="text-3xl font-serif font-semibold text-foreground">
          {amount}
          <span className="text-lg">{unit}</span>
        </h3>
        <p className="text-xs font-sans text-muted-foreground mt-1">{label}</p>
      </div>

      <div className="relative w-20 h-20 mt-auto">
        <svg className="w-full h-full transform -rotate-90">
          <circle
            cx="40"
            cy="40"
            r="34"
            className="stroke-muted fill-none"
            strokeWidth="6"
          />
          <circle
            cx="40"
            cy="40"
            r="34"
            className={`fill-none transition-all duration-500 ${
              color === "primary"
                ? "stroke-primary"
                : color === "secondary"
                ? "stroke-secondary"
                : "stroke-tertiary"
            }`}
            strokeWidth="6"
            strokeDasharray={`${2 * Math.PI * 34}`}
            strokeDashoffset={`${2 * Math.PI * 34 * 0.65}`}
            strokeLinecap="round"
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className={`w-14 h-14 rounded-full flex items-center justify-center ${colorClasses[color]}`}>
            <Icon className="w-6 h-6" />
          </div>
        </div>
      </div>
    </div>
  );
};
