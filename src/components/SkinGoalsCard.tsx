import { Target } from "lucide-react";

export const SkinGoalsCard = () => {
  const goals = [
    { label: "Clear Complexion", progress: 65, color: "primary" },
    { label: "Reduce Redness", progress: 40, color: "secondary" },
    { label: "Hydration", progress: 80, color: "tertiary" },
  ];

  return (
    <div className="bg-card rounded-[32px] shadow-soft p-6">
      <div className="flex items-center gap-3 mb-5">
        <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
          <Target className="w-5 h-5 text-primary" />
        </div>
        <h3 className="text-xl font-serif font-semibold text-foreground">
          Skin Goals
        </h3>
      </div>

      <div className="space-y-4">
        {goals.map((goal, index) => (
          <div key={index}>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-sans text-foreground">{goal.label}</span>
              <span className="text-xs font-sans text-muted-foreground">{goal.progress}%</span>
            </div>
            <div className="h-2 bg-muted rounded-full overflow-hidden">
              <div
                className={`h-full rounded-full transition-all duration-500 ${
                  goal.color === "primary"
                    ? "bg-primary"
                    : goal.color === "secondary"
                    ? "bg-secondary"
                    : "bg-tertiary"
                }`}
                style={{ width: `${goal.progress}%` }}
              />
            </div>
          </div>
        ))}
      </div>

      <div className="flex flex-wrap gap-2 mt-5">
        {["Gentle Care", "Sun Protection", "Barrier Repair"].map((tag, i) => (
          <span
            key={i}
            className="px-3 py-1.5 bg-accent/10 text-accent text-xs font-sans rounded-full"
          >
            {tag}
          </span>
        ))}
      </div>
    </div>
  );
};
