import { LucideIcon } from "lucide-react";

interface ProductCardProps {
  title: string;
  icon: LucideIcon;
  items: string[];
  variant: "good" | "avoid";
}

export const ProductCard = ({ title, icon: Icon, items, variant }: ProductCardProps) => {
  const colorClass = variant === "good" ? "text-emerald-500" : "text-rose-400";
  const bgClass = variant === "good" ? "bg-emerald-500/10" : "bg-rose-400/10";

  return (
    <div className="bg-card rounded-[32px] shadow-soft p-5">
      <div className="flex items-center gap-2 mb-4">
        <div className={`w-9 h-9 rounded-full ${bgClass} flex items-center justify-center`}>
          <Icon className={`w-5 h-5 ${colorClass}`} />
        </div>
        <h3 className="text-base font-serif font-semibold text-foreground">
          {title}
        </h3>
      </div>

      <ul className="space-y-2">
        {items.map((item, index) => (
          <li key={index} className="text-xs font-sans text-muted-foreground flex items-start gap-2">
            <span className={`mt-1 w-1 h-1 rounded-full ${variant === "good" ? "bg-emerald-500" : "bg-rose-400"} flex-shrink-0`} />
            <span>{item}</span>
          </li>
        ))}
      </ul>
    </div>
  );
};
