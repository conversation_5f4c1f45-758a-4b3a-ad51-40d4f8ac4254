import { Header } from "@/components/Header";
import { SkinTypeCard } from "@/components/SkinTypeCard";
import { SkinGoalsCard } from "@/components/SkinGoalsCard";
import { ProductCard } from "@/components/ProductCard";
import { ScanHistory } from "@/components/ScanHistory";
import { BottomNav } from "@/components/BottomNav";
import { ThumbsUp, AlertCircle } from "lucide-react";

const Index = () => {
  return (
    <div className="min-h-screen bg-background pb-24">
      <Header />

      <SkinTypeCard />

      <div className="px-4 mt-6 space-y-4">
        <SkinGoalsCard />

        <div className="grid grid-cols-2 gap-3">
          <ProductCard
            title="Go-To Products"
            icon={ThumbsUp}
            items={["Gentle Cleanser", "Niacinamide Serum", "Ceramide Moisturizer"]}
            variant="good"
          />
          <ProductCard
            title="Avoid Products"
            icon={AlertCircle}
            items={["Harsh Scrubs", "High Alcohol", "Strong Fragrances"]}
            variant="avoid"
          />
        </div>
      </div>

      <ScanHistory />

      <BottomNav />
    </div>
  );
};

export default Index;
