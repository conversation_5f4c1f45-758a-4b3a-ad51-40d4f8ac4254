import { Header } from "@/components/Header";
import { BottomNav } from "@/components/BottomNav";
import {
  Clock,
  CheckCircle,
  AlertCircle,
  MoreVertical,
  Calendar,
  Filter,
} from "lucide-react";

const History = () => {
  // Mock data for scan history - in a real app this would come from an API
  const scanHistory = [
    {
      id: 1,
      productName: "CeraVe Hydrating Cleanser",
      date: "2024-10-25",
      time: "2:30 PM",
      status: "completed",
      score: 85,
      category: "Cleanser",
      image: "/placeholder.svg",
    },
    {
      id: 2,
      productName: "The Ordinary Niacinamide",
      date: "2024-10-25",
      time: "10:15 AM",
      status: "completed",
      score: 92,
      category: "Serum",
      image: "/placeholder.svg",
    },
    {
      id: 3,
      productName: "Neutrogena Ultra Gentle",
      date: "2024-10-24",
      time: "6:45 PM",
      status: "analyzing",
      score: null,
      category: "Cleanser",
      image: "/placeholder.svg",
    },
    {
      id: 4,
      productName: "<PERSON> Toler<PERSON>",
      date: "2024-10-24",
      time: "3:20 PM",
      status: "completed",
      score: 78,
      category: "Moisturizer",
      image: "/placeholder.svg",
    },
    {
      id: 5,
      productName: "Drunk Elephant C-Firma",
      date: "2024-10-23",
      time: "9:10 AM",
      status: "failed",
      score: null,
      category: "Serum",
      image: "/placeholder.svg",
    },
  ];

  // Group scans by date
  const groupedScans = scanHistory.reduce((groups, scan) => {
    const date = scan.date;
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(scan);
    return groups;
  }, {} as Record<string, typeof scanHistory>);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString("en-US", {
        weekday: "long",
        month: "short",
        day: "numeric",
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-5 h-5 text-emerald-500" />;
      case "analyzing":
        return <Clock className="w-5 h-5 text-primary animate-pulse" />;
      case "failed":
        return <AlertCircle className="w-5 h-5 text-rose-400" />;
      default:
        return <Clock className="w-5 h-5 text-muted-foreground" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "Analysis Complete";
      case "analyzing":
        return "Analyzing...";
      case "failed":
        return "Analysis Failed";
      default:
        return "Unknown";
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-emerald-500";
    if (score >= 60) return "text-primary";
    return "text-rose-400";
  };

  return (
    <div className="min-h-screen bg-background pb-32">
      <Header />

      {/* Page Title and Controls */}
      <div className="px-4 mt-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-serif font-semibold text-foreground">
            Scan History
          </h1>
          <div className="flex gap-2">
            <button className="w-10 h-10 rounded-full bg-muted/50 flex items-center justify-center">
              <Filter className="w-5 h-5 text-muted-foreground" />
            </button>
            <button className="w-10 h-10 rounded-full bg-muted/50 flex items-center justify-center">
              <Calendar className="w-5 h-5 text-muted-foreground" />
            </button>
          </div>
        </div>

        {/* Stats Summary */}
        <div className="grid grid-cols-3 gap-3 mb-6">
          <div className="bg-card rounded-[24px] shadow-soft p-4 text-center">
            <h3 className="text-2xl font-serif font-semibold text-foreground">
              {scanHistory.length}
            </h3>
            <p className="text-xs font-sans text-muted-foreground">
              Total Scans
            </p>
          </div>
          <div className="bg-card rounded-[24px] shadow-soft p-4 text-center">
            <h3 className="text-2xl font-serif font-semibold text-emerald-500">
              {scanHistory.filter((s) => s.status === "completed").length}
            </h3>
            <p className="text-xs font-sans text-muted-foreground">Completed</p>
          </div>
          <div className="bg-card rounded-[24px] shadow-soft p-4 text-center">
            <h3 className="text-2xl font-serif font-semibold text-primary">
              {scanHistory.filter((s) => s.status === "analyzing").length}
            </h3>
            <p className="text-xs font-sans text-muted-foreground">
              In Progress
            </p>
          </div>
        </div>
      </div>

      {/* Scan History List */}
      <div className="px-4 space-y-6">
        {Object.entries(groupedScans).map(([date, scans]) => (
          <div key={date}>
            {/* Date Header */}
            <h2 className="text-lg font-serif font-semibold text-foreground mb-4">
              {formatDate(date)}
            </h2>

            {/* Scans for this date */}
            <div className="space-y-3">
              {scans.map((scan) => (
                <div
                  key={scan.id}
                  className="bg-card rounded-[32px] shadow-soft p-4"
                >
                  <div className="flex gap-4">
                    {/* Product Image */}
                    <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl flex items-center justify-center flex-shrink-0">
                      <div className="w-12 h-12 bg-muted/30 rounded-xl"></div>
                    </div>

                    {/* Product Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1 min-w-0">
                          <h3 className="font-sans font-medium text-foreground truncate">
                            {scan.productName}
                          </h3>
                          <p className="text-xs font-sans text-muted-foreground">
                            {scan.category} • {scan.time}
                          </p>
                        </div>
                        <button className="text-muted-foreground hover:text-foreground transition-colors flex-shrink-0 ml-2">
                          <MoreVertical className="w-4 h-4" />
                        </button>
                      </div>

                      {/* Status and Score */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(scan.status)}
                          <span className="text-xs font-sans text-muted-foreground">
                            {getStatusText(scan.status)}
                          </span>
                        </div>

                        {scan.score && (
                          <div className="text-right">
                            <span
                              className={`text-lg font-serif font-semibold ${getScoreColor(
                                scan.score
                              )}`}
                            >
                              {scan.score}
                            </span>
                            <span className="text-xs text-muted-foreground ml-1">
                              / 100
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      <BottomNav />
    </div>
  );
};

export default History;
