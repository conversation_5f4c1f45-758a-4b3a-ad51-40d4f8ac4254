# Skinwise Design System Documentation

## Overview
Skinwise is a mobile-first skincare analysis application with a warm, modern, and elegant design aesthetic. The design system emphasizes soft colors, rounded corners, and a clean interface optimized for mobile devices.

---

## 🎨 Color Palette

### Light Mode
- **Background**: `hsl(28 33% 97%)` - Warm off-white background
- **Foreground**: `hsl(24 12% 18%)` - Dark brown text
- **Card**: `hsl(0 0% 100%)` - Pure white cards
- **Card Foreground**: `hsl(24 12% 18%)` - Dark brown card text

### Primary Colors
- **Primary**: `hsl(18 76% 62%)` - Coral/Peach (Main brand color)
- **Primary Foreground**: `hsl(0 0% 100%)` - White text on primary
- **Secondary**: `hsl(204 68% 62%)` - Sky blue (Accent color)
- **Secondary Foreground**: `hsl(0 0% 100%)` - White text on secondary
- **Tertiary**: `hsl(259 68% 80%)` - Soft lavender (Tertiary accent)
- **Tertiary Foreground**: `hsl(0 0% 100%)` - White text on tertiary

### Utility Colors
- **Muted**: `hsl(28 22% 92%)` - Soft warm gray background
- **Muted Foreground**: `hsl(24 8% 45%)` - Medium gray text
- **Accent**: `hsl(18 76% 62%)` - Same as primary
- **Destructive**: `hsl(0 84% 60%)` - Red for errors/warnings
- **Border**: `hsl(28 25% 88%)` - Soft warm border
- **Input**: `hsl(28 25% 88%)` - Input border color
- **Ring**: `hsl(18 76% 62%)` - Focus ring color

### Dark Mode
- **Background**: `hsl(24 12% 18%)` - Dark brown
- **Foreground**: `hsl(28 33% 97%)` - Light cream
- **Card**: `hsl(24 15% 22%)` - Slightly lighter dark brown
- **Muted**: `hsl(24 10% 28%)` - Dark muted background
- **Border**: `hsl(24 15% 30%)` - Dark border

---

## 📝 Typography

### Font Families
- **Serif**: `Spectral, Georgia, serif` - Used for headings and branding
- **Sans**: `Inter, system-ui, sans-serif` - Used for body text and UI elements

### Font Usage
- **Headings (h1-h6)**: Use `font-serif` class
- **Body & UI**: Use `font-sans` class (default)
- **Brand Name**: "Skinwise" - `font-serif` with `font-semibold`

### Type Scale
- **H1**: `text-3xl` - Primary page headings
- **H2**: `text-2xl` - Section headings
- **H3**: `text-xl` - Subsection headings
- **Body**: `text-base` - Default text size
- **Small**: `text-sm` - Secondary information
- **Tiny**: `text-xs` - Labels and captions

---

## 📐 Spacing & Layout

### Mobile-First Approach
- **Target Device**: Mobile phones (320px - 428px)
- **Container Padding**: `px-4` (16px horizontal padding)
- **Section Spacing**: `gap-6` (24px between major sections)
- **Component Spacing**: `gap-4` (16px between related components)

### Padding Scale
- **Tight**: `p-2` (8px)
- **Default**: `p-4` (16px)
- **Comfortable**: `p-6` (24px)
- **Spacious**: `p-8` (32px)

---

## 🔲 Border Radius

### Radius Scale
- **Base**: `--radius: 2.5rem` (40px) - Large, friendly curves
- **Large**: `rounded-lg` - Cards and major components
- **Medium**: `rounded-md` - Buttons and inputs
- **Small**: `rounded-sm` - Minor elements
- **Circle**: `rounded-full` - Avatar, FAB button

### Usage
- **Cards**: `rounded-lg` with `border` and `shadow-soft`
- **Buttons**: `rounded-md` for interactive elements
- **Avatar**: `rounded-full` for circular elements
- **FAB Button**: `rounded-full` for floating action button

---

## 🌟 Shadows & Elevation

### Shadow Tokens
- **Soft**: `--shadow-soft: 0 4px 20px -4px hsl(28 20% 70% / 0.15)`
  - Subtle elevation for cards and containers
- **Card**: `--shadow-card: 0 8px 32px -8px hsl(28 20% 60% / 0.12)`
  - More prominent shadow for floating elements

### Usage
- **Cards**: Use `shadow-soft` or `shadow-card` classes
- **FAB Button**: Use `shadow-card` for prominence
- **Hover States**: Add `hover:scale-105` for interactive feedback

---

## 🎯 Component Patterns

### Header
- **Height**: `pt-6 pb-4` - Top header with comfortable padding
- **Layout**: Flex container with space-between
- **Brand**: Left-aligned "Skinwise" text (text-3xl, font-serif)
- **Avatar**: Right-aligned circular gradient avatar

### Bottom Navigation
- **Position**: Fixed at bottom, full width
- **Height**: `h-20` (80px)
- **Background**: `bg-card` with `border-t`
- **Items**: 3 nav items evenly distributed
- **Active State**: Full opacity with `text-foreground`
- **Inactive State**: 50% opacity with `text-muted-foreground`
- **Z-Index**: `z-40` for proper layering

### Floating Action Button (FAB)
- **Position**: Fixed `bottom-24 right-6`
- **Size**: `w-14 h-14` (56px circle)
- **Background**: `bg-foreground` (dark in light mode, light in dark mode)
- **Icon**: Plus icon, `w-7 h-7`
- **Effects**: `shadow-card`, `hover:scale-105`, `transition-transform`
- **Z-Index**: `z-50` (above bottom nav)

### Cards
- **Background**: `bg-card`
- **Border**: `border` with `border-border`
- **Shadow**: `shadow-soft` or `shadow-card`
- **Padding**: `p-6` for content
- **Radius**: `rounded-lg`

### Buttons
- **Default**: `bg-primary text-primary-foreground`
- **Secondary**: `bg-secondary text-secondary-foreground`
- **Outline**: `border border-input bg-background`
- **Ghost**: `hover:bg-accent hover:text-accent-foreground`
- **Sizes**: `sm` (h-9), `default` (h-10), `lg` (h-11), `icon` (h-10 w-10)

---

## 🎭 Visual Style

### Design Language
- **Warm & Welcoming**: Coral/peach primary color with warm backgrounds
- **Modern & Clean**: Large border radius, ample whitespace
- **Friendly**: Rounded corners everywhere, soft shadows
- **Professional**: Serif fonts for headings, sans-serif for body

### Gradient Usage
- **Avatar**: `from-primary via-secondary to-tertiary` - Brand gradient
- **Backgrounds**: Subtle gradients using brand colors
- **Effects**: `backdrop-blur-sm` for layered effects

### Imagery
- **Product Images**: Use `aspect-ratio` component for consistent sizing
- **Icons**: Lucide React icons at `w-6 h-6` for nav, `w-5 h-5` for inline

---

## ♿ Accessibility

### Focus States
- **Ring**: Uses `ring-offset-background` and `ring-ring`
- **Visible**: All interactive elements have clear focus indicators
- **Color Contrast**: Meets WCAG AA standards

### Interactive Elements
- **Hover**: Scale or background color changes
- **Active**: Clear visual feedback
- **Disabled**: 50% opacity with `pointer-events-none`

---

## 🔄 Animations

### Transitions
- **Default**: `transition-colors` for color changes
- **Transform**: `transition-transform` for scale/position
- **Duration**: 200-300ms for quick, responsive feel

### Hover Effects
- **Buttons**: `hover:bg-primary/90` - Slight darkening
- **FAB**: `hover:scale-105` - Subtle grow effect
- **Nav Items**: `hover:opacity-100` - Fade in effect

### Animation Classes
- **Accordion**: `accordion-down`, `accordion-up`
- **Fade**: `fade-in`, `fade-out`
- **Scale**: `scale-in`, `scale-out`

---

## 📱 Mobile Optimization

### Viewport
- **Meta**: Responsive viewport configuration
- **Touch Targets**: Minimum 44x44px (following iOS guidelines)
- **Spacing**: Generous padding for thumb-friendly navigation

### Bottom Navigation Safe Area
- **Height**: 80px main navigation
- **FAB Position**: 24px above nav (96px from bottom)
- **Content Padding**: Add `pb-24` or `pb-32` to main content to avoid overlap

### Performance
- **Images**: Lazy loading, optimized sizes
- **Animations**: Hardware-accelerated transforms
- **Fonts**: System font fallbacks for instant rendering

---

## 🎨 Usage Examples

### Creating a New Card Component
```tsx
<Card className="shadow-soft">
  <CardHeader>
    <CardTitle className="text-2xl">Title</CardTitle>
    <CardDescription>Description text</CardDescription>
  </CardHeader>
  <CardContent>
    {/* Content here */}
  </CardContent>
</Card>
```

### Using Color Tokens
```tsx
// ✅ CORRECT - Use semantic tokens
<div className="bg-primary text-primary-foreground">

// ❌ WRONG - Don't use direct colors
<div className="bg-orange-500 text-white">
```

### Responsive Padding
```tsx
// Main content with bottom nav
<main className="px-4 pb-32">
  {/* Content */}
</main>
```

---

## 🚀 Best Practices

1. **Always use design tokens** - Never hardcode colors
2. **Mobile-first** - Design for mobile, enhance for larger screens
3. **Consistent spacing** - Use the spacing scale (4px, 8px, 16px, 24px, 32px)
4. **Semantic HTML** - Use proper heading hierarchy (h1 → h2 → h3)
5. **Accessible icons** - Always provide text labels for navigation
6. **Test in context** - Preview changes on mobile viewport
7. **Theme-aware** - Support both light and dark modes
8. **Performance** - Keep animations smooth with transform/opacity changes

---

## 📚 Design System Files

- **Colors & Tokens**: `src/index.css`
- **Tailwind Config**: `tailwind.config.ts`
- **Component Library**: `src/components/ui/`
- **Custom Components**: `src/components/`

---

*Last Updated: 2025-10-25*
*Version: 1.0*